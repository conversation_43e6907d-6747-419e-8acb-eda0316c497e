package com.enttribe.emailagent.utils;

import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ConnectingIdType;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.misc.ImpersonatedUserId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.cert.X509Certificate;
import javax.net.ssl.HttpsURLConnection;

@Slf4j
@Component
public class EWSUtils {

    // Static block to disable SSL verification globally
    static {
        disableSSLVerification();
    }

    private static String serviceAccountUsername;
    private static String serviceAccountPassword;
    private static String ewsURL;

    @Value("${ews.serviceAccountUsername}")
    public void setServiceAccountUsername(String serviceAccountUsername) {
        EWSUtils.serviceAccountUsername = serviceAccountUsername;
    }

    @Value("${ews.serviceAccountPassword}")
    public void setServiceAccountPassword(String serviceAccountPassword) {
        EWSUtils.serviceAccountPassword = serviceAccountPassword;
    }

    @Value("${ews.ewsURL}")
    public void setEwsURL(String ewsURL) {
        EWSUtils.ewsURL = ewsURL;
    }

    /**
     * Disables SSL certificate verification globally.
     * This is useful when connecting to servers with self-signed certificates or hostname mismatches.
     * WARNING: This should only be used in development or when connecting to trusted internal servers.
     */
    private static void disableSSLVerification() {
        try {
            // Create a trust manager that accepts all certificates
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // Trust all client certificates
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // Trust all server certificates
                    }
                }
            };

            // Install the all-trusting trust manager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            log.info("SSL verification has been disabled for EWS connections");
        } catch (Exception e) {
            log.error("Failed to disable SSL verification", e);
        }
    }

    public static ExchangeService getServiceObjectForUser(String email) throws URISyntaxException {
        log.debug("EWS parameters for @method getServiceObjectForUser are {} {}", email, ewsURL);
        ExchangeService service = getServiceObject();
        ImpersonatedUserId impersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, email);
        service.setImpersonatedUserId(impersonatedUserId);
        return service;
    }

    public static ExchangeService getServiceObject() throws URISyntaxException {
        log.debug("EWS parameters are {} {}", serviceAccountUsername, ewsURL);
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        service.setCredentials(new WebCredentials(serviceAccountUsername, serviceAccountPassword));
        service.setUrl(new URI(ewsURL));
        return service;
    }

}
